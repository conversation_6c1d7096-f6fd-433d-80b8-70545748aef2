import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ProduitService } from '../../services/produit.service';
import { HttpClient } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { Produit } from '../../Model/Produit';

import * as collections from '@angular/cdk/collections';
import {
  MatSnackBar,
  MatSnackBarHorizontalPosition,
  MatSnackBarVerticalPosition,
} from '@angular/material/snack-bar';
import { BehaviorSubject, fromEvent, merge, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { MatMenuTrigger, MatMenuModule } from '@angular/material/menu';
import { SelectionModel } from '@angular/cdk/collections';
import { FormProduitComponent } from '../form-produit/form-produit.component';
import { ProduitDeleteComponent } from '../produit-delete/produit-delete.component';
import { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';
import { Direction } from '@angular/cdk/bidi';
import { TableExportUtil, TableElement } from '@shared';
import { formatDate, NgClass, DatePipe } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRippleModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-produit',
  templateUrl: './produit.component.html',
  styleUrls: ['./produit.component.scss'],
  standalone: true,
  imports: [
    BreadcrumbComponent,
    MatTooltipModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSortModule,
    NgClass,
    MatCheckboxModule,
    MatMenuModule,
    MatRippleModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    // DatePipe,
  ],
})
export class ProduitComponent
  extends UnsubscribeOnDestroyAdapter
  implements OnInit {
  filterToggle = false;
  displayedColumns = [
    'select',
    'codeProd',
    'type',
    'description',
    'prixUnitaireHT',
    'prixUnitaireTTC',
    'tva',
    'actions',
  ];
  exampleDatabase?: ProduitService;
  dataSource!: ProduitDataSource;
  selection = new SelectionModel<Produit>(true, []);
  id?: string;
  produit?: Produit;

  constructor(
    public httpClient: HttpClient,
    public dialog: MatDialog,
    public produitService: ProduitService,
    private snackBar: MatSnackBar
  ) {
    super();
  }

  @ViewChild(MatPaginator, { static: true }) paginator!: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort!: MatSort;
  @ViewChild('filter', { static: true }) filter!: ElementRef;
  @ViewChild(MatMenuTrigger)
  contextMenu?: MatMenuTrigger;
  contextMenuPosition = { x: '0px', y: '0px' };

  ngOnInit() {
    this.loadData();
  }

  refresh() {
    this.loadData();
  }

  addNew() {
  let tempDirection: Direction;
  if (localStorage.getItem('isRtl') === 'true') {
    tempDirection = 'rtl';
  } else {
    tempDirection = 'ltr';
  }
  const dialogRef = this.dialog.open(FormProduitComponent, {
    data: {
      produit: this.produit,
      action: 'add',
    },
    direction: tempDirection,
  });
  this.subs.sink = dialogRef.afterClosed().subscribe((result) => {
    if (result) {
      this.produitService.getAllProduits().subscribe(); // Rafraîchir les données
      this.showNotification(
        'snackbar-success',
        'Produit ajouté avec succès...!!!',
        'bottom',
        'center'
      );
    }
  });
}

editCall(row: Produit) {
  this.id = row.id;
  let tempDirection: Direction;
  if (localStorage.getItem('isRtl') === 'true') {
    tempDirection = 'rtl';
  } else {
    tempDirection = 'ltr';
  }
  const dialogRef = this.dialog.open(FormProduitComponent, {
    data: {
      produit: row,
      action: 'edit',
    },
    direction: tempDirection,
  });
  this.subs.sink = dialogRef.afterClosed().subscribe((result) => {
    if (result) {
      this.produitService.getAllProduits().subscribe(); // Rafraîchir les données
      this.showNotification(
        'black',
        'Produit modifié avec succès...!!!',
        'bottom',
        'center'
      );
    }
  });
}

deleteItem(row: Produit) {
  this.id = row.id;
  let tempDirection: Direction;
  if (localStorage.getItem('isRtl') === 'true') {
    tempDirection = 'rtl';
  } else {
    tempDirection = 'ltr';
  }
  const dialogRef = this.dialog.open(ProduitDeleteComponent, {
    width: '500px',
    maxWidth: '90vw',
    data: row,
    direction: tempDirection,
  });
  this.subs.sink = dialogRef.afterClosed().subscribe((result) => {
    if (result) {
      this.produitService.getAllProduits().subscribe(); // Rafraîchir les données
      this.showNotification(
        'snackbar-danger',
        'Produit supprimé avec succès...!!!',
        'bottom',
        'center'
      );
    }
  });
}

  private refreshTable() {
    this.paginator._changePageSize(this.paginator.pageSize);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.renderedData.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.renderedData.forEach((row) =>
          this.selection.select(row)
        );
  }

  removeSelectedRows() {
    const totalSelect = this.selection.selected.length;
    this.selection.selected.forEach((item) => {
      const index: number = this.dataSource.renderedData.findIndex(
        (d) => d === item
      );
      this.exampleDatabase?.dataChange.value.splice(index, 1);
      this.refreshTable();
      this.selection = new SelectionModel<Produit>(true, []);
    });
    this.showNotification(
      'snackbar-danger',
      totalSelect + ' produits supprimés avec succès...!!!',
      'bottom',
      'center'
    );
  }

  public loadData() {
this.dataSource = new ProduitDataSource(
      this.produitService,
      this.paginator,
      this.sort
    );
    
    this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(
      () => {
        if (!this.dataSource) {
          return;
        }
        this.dataSource.filter = this.filter.nativeElement.value;
      }
    );
  
    this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(
      () => {
        if (!this.dataSource) {
          return;
        }
        this.dataSource.filter = this.filter.nativeElement.value;
      }
    );
  }

  exportExcel() {
    const exportData: Partial<TableElement>[] =
      this.dataSource.filteredData.map((x) => ({
        'Code Produit': x.codeProd,
        Type: x.type,
        Description: x.description,
        'Prix HT': x.prixUnitaireHT,
        'Prix TTC': x.prixUnitaireTTC,
        'TVA (%)': x.tva,
      }));

    TableExportUtil.exportToExcel(exportData, 'produits');
  }

  showNotification(
    colorName: string,
    text: string,
    placementFrom: MatSnackBarVerticalPosition,
    placementAlign: MatSnackBarHorizontalPosition
  ) {
    this.snackBar.open(text, '', {
      duration: 2000,
      verticalPosition: placementFrom,
      horizontalPosition: placementAlign,
      panelClass: colorName,
    });
  }
}

export class ProduitDataSource extends collections.DataSource<Produit> {
  filterChange = new BehaviorSubject('');
  get filter(): string {
    return this.filterChange.value;
  }
  set filter(filter: string) {
    this.filterChange.next(filter);
  }
  filteredData: Produit[] = [];
  renderedData: Produit[] = [];

  constructor(
    public exampleDatabase: ProduitService,
    public paginator: MatPaginator,
    public _sort: MatSort
  ) {
    super();
    this.filterChange.subscribe(() => (this.paginator.pageIndex = 0));
  }

    connect(): Observable<Produit[]> {
    const displayDataChanges = [
      this.exampleDatabase.dataChange,
      this._sort.sortChange,
      this.filterChange,
      this.paginator.page,
    ];
    
    // Chargez les produits via l'API
    this.exampleDatabase.getAllProduits().subscribe({
      next: (produits) => {
        this.exampleDatabase.dataChange.next(produits);
      },
      error: (err) => console.error('Error loading produits', err)
    });
    
    return merge(...displayDataChanges).pipe(
      map(() => {
        this.filteredData = this.exampleDatabase.data
          .slice()
          .filter((produit: Produit) => {
            const searchStr = (
              (produit.codeProd || '') +
              produit.type +
              produit.description +
              produit.prixUnitaireHT +
              produit.prixUnitaireTTC +
              produit.tva
            ).toLowerCase();
            return searchStr.indexOf(this.filter.toLowerCase()) !== -1;
          });
        
        const sortedData = this.sortData(this.filteredData.slice());
        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;
        this.renderedData = sortedData.splice(
          startIndex,
          this.paginator.pageSize
        );
        return this.renderedData;
      })
    );
  }



  disconnect() {}

  sortData(data: Produit[]): Produit[] {
    if (!this._sort.active || this._sort.direction === '') {
      return data;
    }
    return data.sort((a, b) => {
      let propertyA: number | string = '';
      let propertyB: number | string = '';
      switch (this._sort.active) {
        case 'codeProd':
          [propertyA, propertyB] = [a.codeProd || '', b.codeProd || '']; // Gestion du undefined
          break;
        case 'type':
          [propertyA, propertyB] = [a.type, b.type];
          break;
        case 'prixUnitaireHT':
          [propertyA, propertyB] = [a.prixUnitaireHT, b.prixUnitaireHT];
          break;
        case 'prixUnitaireTTC':
          [propertyA, propertyB] = [a.prixUnitaireTTC, b.prixUnitaireTTC];
          break;
        case 'tva':
          [propertyA, propertyB] = [a.tva, b.tva];
          break;
      }
      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;
      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;
      return (
        (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1)
      );
    });
}
}