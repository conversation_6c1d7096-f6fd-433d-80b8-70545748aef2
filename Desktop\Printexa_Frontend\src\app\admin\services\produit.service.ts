import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, throwError, tap, catchError } from 'rxjs';
import { Produit, ProduitDTO, CreateProduitDTO, UpdateProduitDTO } from '../Model/Produit';

// import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProduitService {
  private baseUrl: string = 'https://localhost:5001/api/Produits/';
  private currentProduitSubject: BehaviorSubject<Produit | null>;
  public currentProduit$: Observable<Produit | null>;
  dataChange = new BehaviorSubject<Produit[]>([]);
  dialogData!: Produit;
    isTblLoading = true;


  constructor(private http: HttpClient, private router: Router) {
    this.currentProduitSubject = new BehaviorSubject<Produit | null>(this.getProduitFromStorage());
    this.currentProduit$ = this.currentProduitSubject.asObservable();
  }

  private getProduitFromStorage(): Produit | null {
    const produitData = localStorage.getItem('currentProduit');
    return produitData ? JSON.parse(produitData) : null;
  }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('auth_token');
    return new HttpHeaders({
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {})
    });
  }

  // Récupérer tous les produits
getAllProduits(): Observable<Produit[]> {
    this.isTblLoading = true; // Activation du loading
    return this.http.get<Produit[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(
      tap(produits => {
        this.isTblLoading = false; // Désactivation du loading
        this.dataChange.next(produits);
      }),
      catchError(error => {
        this.isTblLoading = false; // Désactivation en cas d'erreur
        return this.handleError(error);
      })
    );
  }
  // Récupérer un produit par son ID
  getProduitById(id: string): Observable<ProduitDTO> {
    return this.http.get<ProduitDTO>(`${this.baseUrl}/${id}`, { headers: this.getHeaders() }).pipe(
      tap(produit => {
        localStorage.setItem('currentProduit', JSON.stringify(produit));
        this.currentProduitSubject.next(produit);
      }),
      catchError(this.handleError)
    );
  }

  // Créer un nouveau produit
  createProduit(produit: CreateProduitDTO): Observable<ProduitDTO> {
    if (!produit.type || !produit.prixUnitaireHT) {
      return throwError(() => new Error('Type and price are required'));
    }

    return this.http.post<ProduitDTO>(this.baseUrl, produit, { headers: this.getHeaders() }).pipe(
      tap(newProduit => {
        console.log('Produit créé avec succès:', newProduit);
      }),
      catchError(this.handleError)
    );
  }

  // Mettre à jour un produit
  updateProduit(id: string, produit: UpdateProduitDTO): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}${id}`, produit, { headers: this.getHeaders() }).pipe(
      tap(() => {
        console.log('Produit mis à jour avec succès');
        this.clearCurrentProduit();
      }),
      catchError(this.handleError)
    );
  }

  // Supprimer un produit
  deleteProduit(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`, { headers: this.getHeaders() }).pipe(
      tap(() => {
        console.log('Produit supprimé avec succès');
        this.clearCurrentProduit();
      }),
      catchError(this.handleError)
    );
  }

  // Calculer le prix TTC
  calculateTTC(prixHT: number, tva: number): number {
    return prixHT * (1 + tva / 100);
  }

  // Effacer le produit courant
  clearCurrentProduit(): void {
    localStorage.removeItem('currentProduit');
    this.currentProduitSubject.next(null);
  }
get data(): Produit[] {
    return this.dataChange.value;
  }

  getDialogData() {
    return this.dialogData;
  }

  private handleError(error: HttpErrorResponse) {
    console.error('ProduitService error:', error);

    let errorMessage = 'An error occurred';
    const apiError = error.error?.message || error.error?.title;

    if (apiError) {
      errorMessage = apiError;
    } else if (error.status === 0) {
      errorMessage = 'Unable to connect to server';
    } else if (error.status === 400) {
      errorMessage = 'Invalid request data';
    } else if (error.status === 401) {
      errorMessage = 'Unauthorized';
      this.router.navigate(['/login']);
    } else if (error.status === 403) {
      errorMessage = 'Forbidden';
    } else if (error.status === 404) {
      errorMessage = 'Product not found';
    } else if (error.status === 409) {
      errorMessage = 'Conflict - product already exists';
    } else if (error.status >= 500) {
      errorMessage = 'Server error';
    }

    return throwError(() => new Error(errorMessage));
  }
}