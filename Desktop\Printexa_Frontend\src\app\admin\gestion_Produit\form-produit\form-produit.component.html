<div class="addContainer">
  @if (!isDetails) {
  <div class="produit-form">
    <div class="modalHeader">
      <div class="editRowModal">
        <div class="modalHeader clearfix">
          <mat-icon class="product-icon">inventory_2</mat-icon>
          <div class="modal-about">
            {{dialogTitle}}
          </div>
        </div>
      </div>
      <button mat-icon-button (click)="dialogRef.close()" class="modal-close-button" aria-label="Close dialog">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div mat-dialog-content>
      <form class="register-form m-4" [formGroup]="produitForm!" (ngSubmit)="submit()">
        <div class="row">
          <!-- Type de produit -->
          <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2">
            <mat-form-field class="example-full-width mb-3" appearance="outline">
              <mat-label>Type de produit</mat-label>
              <mat-select formControlName="type" required>
                <mat-option *ngFor="let type of typesProduits" [value]="type.value">
                  {{type.label}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="produitForm!.get('type')?.hasError('required')">
                Le type est requis
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Code produit -->
          <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2">
            <mat-form-field class="example-full-width mb-3" appearance="outline">
              <mat-label>Code produit</mat-label>
              <input matInput formControlName="codeProd" placeholder="Ex: PROD001">
              <mat-icon matSuffix>qr_code</mat-icon>
            </mat-form-field>
          </div>

          <!-- Description -->
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2">
            <mat-form-field class="example-full-width mb-3" appearance="outline">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" required rows="3"
                        placeholder="Description détaillée du produit"></textarea>
              <mat-error *ngIf="produitForm!.get('description')?.hasError('required')">
                La description est requise
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Prix unitaire HT -->
          <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 mb-2">
            <mat-form-field class="example-full-width mb-3" appearance="outline">
              <mat-label>Prix unitaire HT</mat-label>
              <input matInput type="number" formControlName="prixUnitaireHT" required
                     min="0" step="0.01" placeholder="0.00">
              <span matSuffix>€</span>
              <mat-error *ngIf="produitForm!.get('prixUnitaireHT')?.hasError('required')">
                Le prix HT est requis
              </mat-error>
              <mat-error *ngIf="produitForm!.get('prixUnitaireHT')?.hasError('min')">
                Le prix doit être positif
              </mat-error>
            </mat-form-field>
          </div>

          <!-- TVA -->
          <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 mb-2">
            <mat-form-field class="example-full-width mb-3" appearance="outline">
              <mat-label>TVA (%)</mat-label>
              <input matInput type="number" formControlName="tva" required
                     min="0" max="100" step="0.1" placeholder="20">
              <span matSuffix>%</span>
              <mat-error *ngIf="produitForm!.get('tva')?.hasError('required')">
                La TVA est requise
              </mat-error>
              <mat-error *ngIf="produitForm!.get('tva')?.hasError('min')">
                La TVA doit être positive
              </mat-error>
              <mat-error *ngIf="produitForm!.get('tva')?.hasError('max')">
                La TVA ne peut pas dépasser 100%
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Prix TTC (calculé automatiquement) -->
          <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 mb-2">
            <mat-form-field class="example-full-width mb-3" appearance="outline">
              <mat-label>Prix unitaire TTC</mat-label>
              <input matInput type="number" [value]="produit.prixUnitaireTTC | number:'1.2-2'"
                     readonly placeholder="0.00">
              <span matSuffix>€</span>
              <mat-hint>Calculé automatiquement</mat-hint>
            </mat-form-field>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2">
            <div class="example-button-row">
              <button mat-raised-button color="primary" type="submit"
                      [disabled]="!produitForm!.valid" class="btn-space">
                <mat-icon>save</mat-icon>
                {{action === 'add' ? 'Ajouter' : 'Modifier'}}
              </button>
              <button mat-raised-button color="warn" (click)="onNoClick()" type="button">
                <mat-icon>cancel</mat-icon>
                Annuler
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  } @else {
  <!-- Mode détails (lecture seule) -->
  <div class="produit-details">
    <div class="modalHeader">
      <div class="editRowModal">
        <div class="modalHeader clearfix">
          <mat-icon class="product-icon">inventory_2</mat-icon>
          <div class="modal-about">
            Détails du produit: {{produit.type}}
          </div>
        </div>
      </div>
      <button mat-icon-button (click)="dialogRef.close()" class="modal-close-button" aria-label="Close dialog">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div mat-dialog-content>
      <div class="details-content m-4">
        <div class="row">
          <div class="col-md-6">
            <div class="detail-item">
              <strong>Type:</strong> {{produit.type}}
            </div>
            <div class="detail-item">
              <strong>Code produit:</strong> {{produit.codeProd || 'Non défini'}}
            </div>
            <div class="detail-item">
              <strong>Prix HT:</strong> {{produit.prixUnitaireHT | currency:'EUR':'symbol':'1.2-2'}}
            </div>
          </div>
          <div class="col-md-6">
            <div class="detail-item">
              <strong>TVA:</strong> {{produit.tva}}%
            </div>
            <div class="detail-item">
              <strong>Prix TTC:</strong> {{produit.prixUnitaireTTC | currency:'EUR':'symbol':'1.2-2'}}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="detail-item">
              <strong>Description:</strong>
              <p>{{produit.description}}</p>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button mat-raised-button color="primary" (click)="onNoClick()">
              <mat-icon>close</mat-icon>
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  }
</div>
