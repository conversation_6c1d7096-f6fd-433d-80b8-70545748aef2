<div class="container">
  <h3 mat-dialog-title>
    <mat-icon class="warning-icon">warning</mat-icon>
    Confirmation de suppression en lot
  </h3>
  
  <div mat-dialog-content>
    <div class="warning-message">
      <p>
        <strong>Attention !</strong> Vous êtes sur le point de supprimer définitivement 
        <strong>{{data.totalCount}} produit(s)</strong>. Cette action est irréversible.
      </p>
    </div>

    <div class="summary-section">
      <h4>Résumé de la sélection :</h4>
      
      <div class="summary-stats">
        <div class="stat-item">
          <mat-icon>inventory_2</mat-icon>
          <span class="stat-label">Nombre de produits :</span>
          <span class="stat-value">{{data.totalCount}}</span>
        </div>
        
        <div class="stat-item">
          <mat-icon>euro</mat-icon>
          <span class="stat-label">Valeur totale TTC :</span>
          <span class="stat-value">{{getTotalValue() | currency:'EUR':'symbol':'1.2-2'}}</span>
        </div>
      </div>

      <div class="types-summary" *ngIf="getProductTypesKeys().length > 0">
        <h5>Répartition par type :</h5>
        <div class="type-list">
          <div class="type-item" *ngFor="let type of getProductTypesKeys()">
            <span class="type-name">{{type}}</span>
            <span class="type-count">{{getProductTypesSummary()[type]}} produit(s)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Affichage des premiers produits si la liste n'est pas trop longue -->
    <div class="products-preview" *ngIf="data.selectedProducts.length <= 5">
      <h5>Produits à supprimer :</h5>
      <div class="product-list">
        <div class="product-item" *ngFor="let product of data.selectedProducts">
          <div class="product-info">
            <span class="product-type">{{product.type}}</span>
            <span class="product-description">{{product.description}}</span>
            <span class="product-price">{{product.prixUnitaireTTC | currency:'EUR':'symbol':'1.2-2'}}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Message pour les grandes sélections -->
    <div class="large-selection-message" *ngIf="data.selectedProducts.length > 5">
      <p>
        <mat-icon>info</mat-icon>
        Trop de produits sélectionnés pour les afficher individuellement. 
        Consultez le résumé ci-dessus.
      </p>
    </div>
  </div>
  
  <div mat-dialog-actions class="mb-1">
    <button mat-flat-button color="warn" (click)="confirmDelete()" class="delete-btn">
      <mat-icon>delete_forever</mat-icon>
      Supprimer tout
    </button>
    <button mat-flat-button (click)="onNoClick()" class="cancel-btn">
      <mat-icon>cancel</mat-icon>
      Annuler
    </button>
  </div>
</div>
