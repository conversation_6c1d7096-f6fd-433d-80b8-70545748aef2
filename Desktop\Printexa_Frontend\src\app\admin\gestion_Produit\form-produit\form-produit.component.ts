import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Component, Inject } from '@angular/core';
import { ProduitService } from '../../services/produit.service';
import { UntypedFormControl, Validators, UntypedFormGroup, UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Produit, ProduitModel, CreateProduitDTO, UpdateProduitDTO } from '../../Model/Produit';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';

export interface DialogData {
  id: string;
  action: string;
  produit: Produit;
}

@Component({
  selector: 'app-form-produit',
  templateUrl: './form-produit.component.html',
  styleUrls: ['./form-produit.component.scss'],
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCardModule,
    CommonModule,
  ],
})
export class FormProduitComponent {
  action: string;
  dialogTitle?: string;
  isDetails = false;
  produitForm?: UntypedFormGroup;
  produit: ProduitModel;

  // Types de produits disponibles
  typesProduits = [
    { value: 'Service', label: 'Service' },
    { value: 'Produit', label: 'Produit' },
    { value: 'Matériel', label: 'Matériel' },
    { value: 'Logiciel', label: 'Logiciel' },
    { value: 'Formation', label: 'Formation' },
    { value: 'Consultation', label: 'Consultation' }
  ];

  constructor(
    public dialogRef: MatDialogRef<FormProduitComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    public produitService: ProduitService,
    private fb: UntypedFormBuilder
  ) {
    // Set the defaults
    this.action = data.action;
    if (this.action === 'edit') {
      this.isDetails = false;
      this.dialogTitle = `Modifier: ${data.produit.type}`;
      this.produit = new ProduitModel(data.produit);
      this.produitForm = this.createProduitForm();
    } else if (this.action === 'details') {
      this.produit = new ProduitModel(data.produit);
      this.isDetails = true;
    } else {
      this.isDetails = false;
      this.dialogTitle = 'Nouveau Produit';
      const blankObject = {} as Produit;
      this.produit = new ProduitModel(blankObject);
      this.produitForm = this.createProduitForm();
    }
  }

  formControl = new UntypedFormControl('', [
    Validators.required,
  ]);

  getErrorMessage() {
    return this.formControl.hasError('required')
      ? 'Champ requis'
      : this.formControl.hasError('min')
      ? 'La valeur doit être positive'
      : '';
  }

  createProduitForm(): UntypedFormGroup {
    const form = this.fb.group({
      id: [this.produit.id],
      type: [this.produit.type, [Validators.required]],
      description: [this.produit.description, [Validators.required]],
      prixUnitaireHT: [this.produit.prixUnitaireHT, [Validators.required, Validators.min(0)]],
      tva: [this.produit.tva, [Validators.required, Validators.min(0), Validators.max(100)]],
      codeProd: [this.produit.codeProd],
    });

    // Calculer automatiquement le prix TTC quand le prix HT ou la TVA change
    form.get('prixUnitaireHT')?.valueChanges.subscribe(() => {
      this.calculateTTC();
    });

    form.get('tva')?.valueChanges.subscribe(() => {
      this.calculateTTC();
    });

    return form;
  }

  calculateTTC() {
    if (this.produitForm) {
      const prixHT = this.produitForm.get('prixUnitaireHT')?.value || 0;
      const tva = this.produitForm.get('tva')?.value || 0;
      const prixTTC = this.produitService.calculateTTC(prixHT, tva);
      this.produit.prixUnitaireTTC = prixTTC;
    }
  }

  submit() {
    if (this.produitForm?.valid) {
      if (this.action === 'add') {
        this.confirmAdd();
      } else if (this.action === 'edit') {
        this.confirmEdit();
      }
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  public confirmAdd(): void {
    if (this.produitForm?.valid) {
      const formValue = this.produitForm.getRawValue();
      const createDto: CreateProduitDTO = {
        type: formValue.type,
        description: formValue.description,
        prixUnitaireHT: formValue.prixUnitaireHT,
        tva: formValue.tva,
        codeProd: formValue.codeProd
      };

      this.produitService.createProduit(createDto).subscribe({
        next: (result) => {
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error('Erreur lors de la création du produit:', error);
        }
      });
    }
  }

  public confirmEdit(): void {
    if (this.produitForm?.valid) {
      const formValue = this.produitForm.getRawValue();
      const updateDto: UpdateProduitDTO = {
        type: formValue.type,
        description: formValue.description,
        prixUnitaireHT: formValue.prixUnitaireHT,
        tva: formValue.tva,
        codeProd: formValue.codeProd
      };

      this.produitService.updateProduit(this.produit.id, updateDto).subscribe({
        next: () => {
          this.dialogRef.close(true);
        },
        error: (error) => {
          console.error('Erreur lors de la modification du produit:', error);
        }
      });
    }
  }
}
