import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { Component, Inject } from '@angular/core';
import { ProduitService } from '../../services/produit.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';

export interface DialogData {
  id: string;
  type: string;
  description: string;
  prixUnitaireHT: number;
  prixUnitaireTTC: number;
  tva: number;
  codeProd?: string;
}

@Component({
  selector: 'app-produit-delete',
  templateUrl: './produit-delete.component.html',
  styleUrls: ['./produit-delete.component.scss'],
  standalone: true,
  imports: [
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatButtonModule,
    MatIconModule,
    MatDialogClose,
    CommonModule,
  ],
})
export class ProduitDeleteComponent {
  constructor(
    public dialogRef: MatDialogRef<ProduitDeleteComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    public produitService: ProduitService
  ) {}

  onNoClick(): void {
    this.dialogRef.close();
  }

  confirmDelete(): void {
    this.produitService.deleteProduit(this.data.id).subscribe({
      next: () => {
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Erreur lors de la suppression du produit:', error);
        this.dialogRef.close(false);
      }
    });
  }
}
